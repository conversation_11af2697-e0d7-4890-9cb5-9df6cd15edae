'use client'

import Link from 'next/link'
import { useState } from 'react'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  return (
    <header className="bg-white/95 backdrop-blur-sm border-b border-neutral-100 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link href="/" className="text-2xl font-display font-bold text-primary">
            Tea House Kigali
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link href="/restaurant" className="text-foreground hover:text-primary transition-colors">
              Restaurant
            </Link>
            <Link href="/coworking" className="text-foreground hover:text-primary transition-colors">
              Co-working
            </Link>
            <Link href="/hotel" className="text-foreground hover:text-primary transition-colors">
              Hotel
            </Link>
            <Link href="/gallery" className="text-foreground hover:text-primary transition-colors">
              Gallery
            </Link>
            <Link href="/contact" className="text-foreground hover:text-primary transition-colors">
              Contact
            </Link>
          </nav>

          {/* Book Button */}
          <button className="hidden md:block bg-gold hover:bg-gold-light text-white px-6 py-2 rounded-full font-medium transition-colors">
            Book Now
          </button>

          {/* Mobile Menu Button */}
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 text-foreground hover:text-primary"
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden py-4 border-t border-neutral-100">
            <div className="flex flex-col space-y-4">
              <Link href="/restaurant" className="text-foreground hover:text-primary transition-colors">
                Restaurant
              </Link>
              <Link href="/coworking" className="text-foreground hover:text-primary transition-colors">
                Co-working
              </Link>
              <Link href="/hotel" className="text-foreground hover:text-primary transition-colors">
                Hotel
              </Link>
              <Link href="/gallery" className="text-foreground hover:text-primary transition-colors">
                Gallery
              </Link>
              <Link href="/contact" className="text-foreground hover:text-primary transition-colors">
                Contact
              </Link>
              <button className="bg-gold hover:bg-gold-light text-white px-6 py-2 rounded-full font-medium transition-colors w-fit">
                Book Now
              </button>
            </div>
          </nav>
        )}
      </div>
    </header>
  )
}