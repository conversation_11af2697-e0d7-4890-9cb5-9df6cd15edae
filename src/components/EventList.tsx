import Link from 'next/link'
import siteData from '@/content/site.json'

interface Event {
  slug: string
  title: string
  date: string
  summary: string
  description: string
  image: string
  capacity: string
}

function EventCard({ event }: { event: Event }) {
  const eventDate = new Date(event.date)
  const isUpcoming = eventDate > new Date()
  
  return (
    <div className="bg-white rounded-2xl shadow-sm hover:shadow-lg transition-shadow overflow-hidden">
      <div className="aspect-video bg-gradient-to-br from-avocado/20 to-gold/20 flex items-center justify-center">
        {/* Placeholder for event image */}
        <div className="text-center text-gray-500">
          <svg className="w-16 h-16 mx-auto mb-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clipRule="evenodd" />
          </svg>
          <p className="text-sm">{event.title}</p>
        </div>
      </div>
      
      <div className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-2xl font-display font-bold text-foreground">{event.title}</h3>
          {isUpcoming && (
            <span className="bg-gold/20 text-gold px-3 py-1 rounded-full text-sm font-medium">
              Upcoming
            </span>
          )}
        </div>
        
        <div className="flex items-center text-gray-600 mb-4">
          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
          </svg>
          <span>{eventDate.toLocaleDateString('en-US', { 
            weekday: 'long',
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
          })}</span>
        </div>
        
        <p className="text-gray-600 mb-4 leading-relaxed">{event.summary}</p>
        <p className="text-sm text-gray-500 mb-6">{event.capacity}</p>
        
        <button className="w-full bg-primary hover:bg-primary-dark text-white py-3 rounded-full font-medium transition-colors">
          Book Event
        </button>
      </div>
    </div>
  )
}

export default function EventList() {
  const events = siteData.events

  return (
    <section id="events" className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-display font-bold text-foreground mb-4">
            Special Events
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Join us for memorable dining experiences, from weekly brunches to special celebrations.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {events.map((event) => (
            <EventCard key={event.slug} event={event} />
          ))}
        </div>

        <div className="bg-gradient-to-r from-avocado/10 to-gold/10 rounded-2xl p-8 text-center">
          <h3 className="text-2xl font-display font-bold text-foreground mb-4">
            Private Events
          </h3>
          <p className="text-lg text-gray-600 mb-6">
            Planning a special celebration? We offer customized private dining experiences 
            for birthdays, anniversaries, business meetings, and more.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href={`tel:${siteData.brand.phone}`}
              className="bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              Call to Book
            </a>
            <a 
              href={`https://wa.me/${siteData.brand.whatsapp.replace(/[^0-9]/g, '')}`}
              target="_blank"
              rel="noopener noreferrer"
              className="bg-gold hover:bg-gold-light text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              WhatsApp Us
            </a>
          </div>
        </div>
      </div>
    </section>
  )
}