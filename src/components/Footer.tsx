import Link from 'next/link'

export default function Footer() {
  return (
    <footer className="bg-neutral-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand & Contact */}
          <div className="col-span-1 md:col-span-2">
            <h3 className="text-2xl font-display font-bold text-gold mb-4">
              Tea House Kigali
            </h3>
            <div className="space-y-2 text-neutral-300">
              <p>[Street], Kigali, Rwanda</p>
              <p>Mon–Sun 7:30–20:00</p>
              <a href="tel:+250700000000" className="block hover:text-gold transition-colors">
                +250 7xx xxx xxx
              </a>
              <a href="mailto:<EMAIL>" className="block hover:text-gold transition-colors">
                <EMAIL>
              </a>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h4 className="font-semibold text-gold mb-4">Quick Links</h4>
            <nav className="space-y-2">
              <Link href="/restaurant" className="block text-neutral-300 hover:text-gold transition-colors">
                Restaurant
              </Link>
              <Link href="/coworking" className="block text-neutral-300 hover:text-gold transition-colors">
                Co-working
              </Link>
              <Link href="/hotel" className="block text-neutral-300 hover:text-gold transition-colors">
                Hotel
              </Link>
              <Link href="/gallery" className="block text-neutral-300 hover:text-gold transition-colors">
                Gallery
              </Link>
            </nav>
          </div>

          {/* Newsletter */}
          <div>
            <h4 className="font-semibold text-gold mb-4">Newsletter</h4>
            <form className="space-y-3">
              <input
                type="email"
                placeholder="Your email"
                className="w-full px-3 py-2 rounded-md bg-neutral-800 border border-neutral-700 text-white placeholder-neutral-400 focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent"
              />
              <button
                type="submit"
                className="w-full bg-gold hover:bg-gold-light text-white py-2 rounded-md font-medium transition-colors"
              >
                Subscribe
              </button>
            </form>
          </div>
        </div>

        {/* Social & Legal */}
        <div className="mt-8 pt-8 border-t border-neutral-800 flex flex-col md:flex-row justify-between items-center">
          <div className="flex space-x-6 mb-4 md:mb-0">
            <a href="https://instagram.com/teahousekigali" className="text-neutral-400 hover:text-gold transition-colors">
              Instagram
            </a>
            <a href="https://facebook.com/teahousekigali" className="text-neutral-400 hover:text-gold transition-colors">
              Facebook
            </a>
            <a href="https://tiktok.com/@teahousekigali" className="text-neutral-400 hover:text-gold transition-colors">
              TikTok
            </a>
          </div>
          <div className="flex space-x-4 text-neutral-400 text-sm">
            <Link href="/privacy" className="hover:text-gold transition-colors">
              Privacy Policy
            </Link>
            <Link href="/terms" className="hover:text-gold transition-colors">
              Terms of Service
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
}