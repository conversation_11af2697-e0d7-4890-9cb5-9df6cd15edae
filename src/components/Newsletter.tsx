'use client'

import { useState } from 'react'

export default function Newsletter() {
  const [email, setEmail] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [message, setMessage] = useState('')

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    // Simulate API call for now
    setTimeout(() => {
      setMessage('Thank you for subscribing! Check your email for confirmation.')
      setEmail('')
      setIsSubmitting(false)
    }, 1000)
  }

  return (
    <section className="py-16 bg-gradient-to-r from-primary to-avocado text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="max-w-3xl mx-auto text-center">
          <h2 className="text-4xl font-display font-bold mb-4">
            Stay Connected
          </h2>
          <p className="text-xl opacity-90 mb-8">
            Subscribe to our newsletter for special events, seasonal menus, 
            and exclusive offers at Tea House Kigali.
          </p>

          <form onSubmit={handleSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter your email"
              required
              className="flex-1 px-4 py-3 rounded-full text-foreground focus:outline-none focus:ring-2 focus:ring-gold focus:ring-offset-2 focus:ring-offset-primary"
            />
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-gold hover:bg-gold-light text-white px-8 py-3 rounded-full font-medium transition-colors disabled:opacity-50 whitespace-nowrap"
            >
              {isSubmitting ? 'Subscribing...' : 'Subscribe'}
            </button>
          </form>

          {message && (
            <p className="mt-4 text-gold-light">{message}</p>
          )}

          <p className="text-sm opacity-75 mt-4">
            We respect your privacy. Unsubscribe at any time.
          </p>
        </div>
      </div>
    </section>
  )
}