import Link from 'next/link'

const galleryImages = [
  { src: '/images/gallery/interior-1.jpg', alt: 'Tea House interior' },
  { src: '/images/gallery/food-1.jpg', alt: 'Delicious breakfast' },
  { src: '/images/gallery/cowork-1.jpg', alt: 'Co-working space' },
  { src: '/images/gallery/hotel-1.jpg', alt: 'Hotel room' },
  { src: '/images/gallery/outdoor-1.jpg', alt: 'Outdoor seating' },
  { src: '/images/gallery/tea-1.jpg', alt: 'Tea service' },
  { src: '/images/gallery/massage-1.jpg', alt: 'Massage room' },
  { src: '/images/gallery/event-1.jpg', alt: 'Evening event' }
]

export default function GalleryStrip() {
  return (
    <section className="py-16 bg-neutral-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-display font-bold text-foreground mb-4">
            A Glimpse Inside
          </h2>
          <p className="text-xl text-gray-600">
            Discover the beauty and atmosphere of Tea House Kigali
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          {galleryImages.map((image, index) => (
            <div
              key={index}
              className="aspect-square bg-gradient-to-br from-avocado/10 to-gold/10 rounded-2xl flex items-center justify-center overflow-hidden group cursor-pointer hover:scale-[1.02] transition-transform"
            >
              {/* Placeholder for images */}
              <div className="text-center text-gray-500">
                <svg className="w-12 h-12 mx-auto mb-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                </svg>
                <p className="text-xs">{image.alt}</p>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center">
          <Link
            href="/gallery"
            className="inline-flex items-center bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-full font-medium transition-colors"
          >
            View Full Gallery
            <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  )
}