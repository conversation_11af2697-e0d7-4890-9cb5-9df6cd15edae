import Link from 'next/link'

export default function Hero() {
  return (
    <section className="relative bg-gradient-to-br from-avocado/10 to-gold/10 min-h-screen flex items-center">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
        <div>
          <h1 className="text-5xl lg:text-6xl font-display font-bold text-foreground mb-6">
            Welcome to <span className="text-primary">Tea House</span>{' '}
            <span className="text-gold">Kigali</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 leading-relaxed">
            Experience the perfect blend of exceptional dining, productive co-working, 
            and comfortable accommodation in the heart of Kigali. Where every visit 
            is a moment of comfort and inspiration.
          </p>
          
          {/* CTAs */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
            <Link 
              href="/restaurant#events" 
              className="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-full font-medium text-center transition-colors"
            >
              Restaurant Events
            </Link>
            <Link 
              href="/restaurant#order" 
              className="bg-gold hover:bg-gold-light text-white px-6 py-3 rounded-full font-medium text-center transition-colors"
            >
              Order Food
            </Link>
            <Link 
              href="/coworking" 
              className="bg-avocado hover:bg-avocado-dark text-white px-6 py-3 rounded-full font-medium text-center transition-colors"
            >
              Co-working
            </Link>
            <Link 
              href="/hotel#rooms" 
              className="bg-accent hover:bg-accent-light text-white px-6 py-3 rounded-full font-medium text-center transition-colors"
            >
              Book Rooms
            </Link>
          </div>
        </div>
        
        <div className="relative">
          <div className="aspect-square bg-gradient-to-br from-avocado/20 to-gold/20 rounded-3xl flex items-center justify-center">
            {/* Placeholder for hero image */}
            <div className="text-center text-gray-500">
              <svg className="w-24 h-24 mx-auto mb-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
              </svg>
              <p className="text-sm">Tea House Hero Image</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}