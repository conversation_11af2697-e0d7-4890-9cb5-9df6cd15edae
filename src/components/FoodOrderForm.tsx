'use client'

import { useState } from 'react'
import siteData from '@/content/site.json'

export default function FoodOrderForm() {
  const [selectedItems, setSelectedItems] = useState<{[key: string]: number}>({})
  const [orderType, setOrderType] = useState<'pickup' | 'delivery'>('pickup')
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    phone: '',
    email: '',
    location: '',
    notes: ''
  })

  const handleItemChange = (itemId: string, quantity: number) => {
    if (quantity === 0) {
      const newItems = { ...selectedItems }
      delete newItems[itemId]
      setSelectedItems(newItems)
    } else {
      setSelectedItems({ ...selectedItems, [itemId]: quantity })
    }
  }

  const getItemById = (id: string) => {
    const allItems = [
      ...siteData.menu.breakfast,
      ...siteData.menu.salads,
      ...siteData.menu.mains,
      ...siteData.menu.drinks
    ]
    return allItems.find(item => item.id === id)
  }

  const getTotalPrice = () => {
    return Object.entries(selectedItems).reduce((total, [itemId, quantity]) => {
      const item = getItemById(itemId)
      return total + (item ? item.price * quantity : 0)
    }, 0)
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // This will be connected to Tally form later
    alert('Order form submitted! We will contact you to confirm your order.')
  }

  return (
    <section id="order" className="py-16 bg-neutral-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl lg:text-5xl font-display font-bold text-foreground mb-4">
            Order Food
          </h2>
          <p className="text-xl text-gray-600">
            Place your order for pickup or delivery. Payment on pickup/delivery.
          </p>
        </div>

        <div className="bg-white rounded-2xl p-8 shadow-sm">
          <form onSubmit={handleSubmit}>
            {/* Order Type */}
            <div className="mb-8">
              <label className="block text-sm font-medium text-gray-700 mb-4">Order Type</label>
              <div className="flex gap-4">
                <button
                  type="button"
                  onClick={() => setOrderType('pickup')}
                  className={`flex-1 py-3 px-4 rounded-xl font-medium transition-colors ${
                    orderType === 'pickup' 
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  🚶‍♀️ Pickup
                </button>
                <button
                  type="button"
                  onClick={() => setOrderType('delivery')}
                  className={`flex-1 py-3 px-4 rounded-xl font-medium transition-colors ${
                    orderType === 'delivery' 
                      ? 'bg-primary text-white' 
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  🚗 Delivery
                </button>
              </div>
            </div>

            {/* Quick Order Options */}
            <div className="mb-8">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Order</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                {siteData.menu.breakfast.slice(0, 4).map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-xl">
                    <div>
                      <h4 className="font-medium text-gray-900">{item.name}</h4>
                      <p className="text-sm text-gray-600">{item.price.toLocaleString()} RWF</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        type="button"
                        onClick={() => handleItemChange(item.id, Math.max(0, (selectedItems[item.id] || 0) - 1))}
                        className="w-8 h-8 rounded-full bg-gray-200 text-gray-700 hover:bg-gray-300 transition-colors"
                      >
                        −
                      </button>
                      <span className="w-8 text-center font-medium">{selectedItems[item.id] || 0}</span>
                      <button
                        type="button"
                        onClick={() => handleItemChange(item.id, (selectedItems[item.id] || 0) + 1)}
                        className="w-8 h-8 rounded-full bg-primary text-white hover:bg-primary-dark transition-colors"
                      >
                        +
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Customer Information */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div>
                <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                  Full Name *
                </label>
                <input
                  type="text"
                  id="name"
                  required
                  value={customerInfo.name}
                  onChange={(e) => setCustomerInfo({...customerInfo, name: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  id="phone"
                  required
                  value={customerInfo.phone}
                  onChange={(e) => setCustomerInfo({...customerInfo, phone: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  value={customerInfo.email}
                  onChange={(e) => setCustomerInfo({...customerInfo, email: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
              </div>
              
              {orderType === 'delivery' && (
                <div>
                  <label htmlFor="location" className="block text-sm font-medium text-gray-700 mb-2">
                    Delivery Location *
                  </label>
                  <input
                    type="text"
                    id="location"
                    required
                    value={customerInfo.location}
                    onChange={(e) => setCustomerInfo({...customerInfo, location: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Enter your delivery address"
                  />
                </div>
              )}
            </div>

            <div className="mb-8">
              <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                Special Instructions
              </label>
              <textarea
                id="notes"
                rows={3}
                value={customerInfo.notes}
                onChange={(e) => setCustomerInfo({...customerInfo, notes: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                placeholder="Any special requests or dietary restrictions..."
              />
            </div>

            {/* Order Summary */}
            {Object.keys(selectedItems).length > 0 && (
              <div className="bg-avocado/10 rounded-xl p-6 mb-8">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                {Object.entries(selectedItems).map(([itemId, quantity]) => {
                  const item = getItemById(itemId)
                  return item ? (
                    <div key={itemId} className="flex justify-between items-center mb-2">
                      <span>{item.name} × {quantity}</span>
                      <span className="font-medium">{(item.price * quantity).toLocaleString()} RWF</span>
                    </div>
                  ) : null
                })}
                <div className="border-t border-gray-300 pt-2 mt-4">
                  <div className="flex justify-between items-center font-bold text-lg">
                    <span>Total</span>
                    <span className="text-primary">{getTotalPrice().toLocaleString()} RWF</span>
                  </div>
                </div>
              </div>
            )}

            <div className="flex flex-col sm:flex-row gap-4">
              <button
                type="submit"
                className="flex-1 bg-primary hover:bg-primary-dark text-white py-3 px-6 rounded-full font-medium transition-colors"
              >
                Place Order
              </button>
              <a
                href={`https://wa.me/${siteData.brand.whatsapp.replace(/[^0-9]/g, '')}`}
                target="_blank"
                rel="noopener noreferrer"
                className="flex-1 bg-gold hover:bg-gold-light text-white py-3 px-6 rounded-full font-medium text-center transition-colors"
              >
                Order via WhatsApp
              </a>
            </div>

            <p className="text-sm text-gray-500 text-center mt-4">
              * We will contact you to confirm your order and arrange payment on {orderType}.
            </p>
          </form>
        </div>
      </div>
    </section>
  )
}