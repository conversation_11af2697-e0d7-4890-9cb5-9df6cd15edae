import siteData from '@/content/site.json'

interface MenuItem {
  id: string
  name: string
  desc: string
  price: number
}

interface MenuSectionProps {
  title: string
  items: MenuItem[]
  icon: string
}

function MenuSection({ title, items, icon }: MenuSectionProps) {
  return (
    <div className="mb-12">
      <div className="flex items-center mb-6">
        <span className="text-3xl mr-3">{icon}</span>
        <h3 className="text-3xl font-display font-bold text-foreground">{title}</h3>
      </div>
      <div className="grid gap-6">
        {items.map((item) => (
          <div key={item.id} className="flex justify-between items-start bg-white p-6 rounded-2xl shadow-sm hover:shadow-md transition-shadow">
            <div className="flex-1">
              <h4 className="text-xl font-semibold text-foreground mb-2">{item.name}</h4>
              <p className="text-gray-600 leading-relaxed">{item.desc}</p>
            </div>
            <div className="ml-6 text-right">
              <span className="text-2xl font-bold text-primary">{item.price.toLocaleString()}</span>
              <span className="text-sm text-gray-500 block">RWF</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default function MenuList() {
  const menu = siteData.menu

  return (
    <section id="menu" className="py-16 bg-neutral-50">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl lg:text-5xl font-display font-bold text-foreground mb-4">
            Our Menu
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Fresh, locally-sourced ingredients crafted into delicious meals. 
            All hotel guests enjoy complimentary breakfast.
          </p>
        </div>

        <MenuSection title="Breakfast" items={menu.breakfast} icon="🌅" />
        <MenuSection title="Salads" items={menu.salads} icon="🥗" />
        <MenuSection title="Main Courses" items={menu.mains} icon="🍽️" />
        <MenuSection title="Beverages" items={menu.drinks} icon="☕" />

        <div className="text-center mt-12 p-8 bg-gradient-to-r from-avocado/10 to-gold/10 rounded-2xl">
          <p className="text-lg text-gray-700 mb-4">
            <strong>Hotel Guests:</strong> Breakfast is included with your stay!
          </p>
          <p className="text-gray-600">
            Lunch and dinner service available during restaurant hours: {siteData.brand.hours}
          </p>
        </div>
      </div>
    </section>
  )
}