import Link from 'next/link'

const services = [
  {
    title: 'Restaurant',
    description: 'Fresh, locally-sourced cuisine with our signature tea blends and seasonal specialties.',
    icon: '🍽️',
    link: '/restaurant',
    color: 'bg-primary/10 text-primary'
  },
  {
    title: 'Co-working',
    description: 'Productive workspace with fast Wi-Fi, meeting rooms, and complimentary beverages.',
    icon: '💻',
    link: '/coworking',
    color: 'bg-avocado/10 text-avocado-dark'
  },
  {
    title: 'Boutique Hotel',
    description: 'Comfortable rooms with breakfast included and relaxing massage services.',
    icon: '🏨',
    link: '/hotel',
    color: 'bg-gold/10 text-gold'
  }
]

export default function ServiceHighlights() {
  return (
    <section className="py-16 bg-neutral-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-display font-bold text-foreground mb-4">
            Three Experiences, One Place
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Whether you're dining, working, or staying with us, Tea House Kigali offers 
            exceptional service and comfort in every experience.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <Link 
              key={index} 
              href={service.link}
              className="group bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
            >
              <div className={`w-16 h-16 rounded-full ${service.color} flex items-center justify-center text-2xl mb-6 group-hover:scale-110 transition-transform`}>
                {service.icon}
              </div>
              <h3 className="text-2xl font-display font-semibold text-foreground mb-4">
                {service.title}
              </h3>
              <p className="text-gray-600 leading-relaxed">
                {service.description}
              </p>
              <div className="mt-6 flex items-center text-primary font-medium group-hover:text-primary-dark transition-colors">
                Learn more
                <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}