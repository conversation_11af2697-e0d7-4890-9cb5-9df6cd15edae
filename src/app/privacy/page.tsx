import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Privacy Policy - Tea House Kigali',
  description: 'Privacy Policy for Tea House Kigali.',
}

export default function Privacy() {
  return (
    <div className="min-h-screen py-16">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-4xl font-display font-bold mb-8">Privacy Policy</h1>
        <div className="prose prose-lg max-w-none">
          <p className="text-lg text-gray-600 mb-6">Last updated: {new Date().toLocaleDateString()}</p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Information We Collect</h2>
          <p>We collect information you provide directly to us, such as when you make a reservation, sign up for our newsletter, or contact us.</p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">How We Use Your Information</h2>
          <p>We use the information we collect to provide, maintain, and improve our services, process bookings, and communicate with you.</p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Data Retention</h2>
          <p>We retain your personal information for as long as necessary to fulfill the purposes outlined in this policy.</p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Contact Us</h2>
          <p>If you have any questions about this Privacy Policy, please contact <NAME_EMAIL>.</p>
        </div>
      </div>
    </div>
  )
}