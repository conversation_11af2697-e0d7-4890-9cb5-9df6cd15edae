import Hero from '@/components/Hero'
import ServiceHighlights from '@/components/ServiceHighlights'
import Testimonials from '@/components/Testimonials'
import GalleryStrip from '@/components/GalleryStrip'
import Newsletter from '@/components/Newsletter'
import siteData from '@/content/site.json'

export default function Home() {
  return (
    <>
      <Hero />
      <ServiceHighlights />
      <Testimonials />
      <GalleryStrip />
      
      {/* Contact Info Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <h3 className="text-2xl font-display font-semibold text-foreground mb-4">
                Visit Us
              </h3>
              <p className="text-gray-600 mb-2">{siteData.brand.address}</p>
              <p className="text-gray-600">{siteData.brand.hours}</p>
            </div>
            
            <div>
              <h3 className="text-2xl font-display font-semibold text-foreground mb-4">
                Contact
              </h3>
              <a 
                href={`tel:${siteData.brand.phone}`}
                className="block text-primary hover:text-primary-dark transition-colors mb-2"
              >
                {siteData.brand.phone}
              </a>
              <a 
                href={`mailto:${siteData.brand.email}`}
                className="block text-primary hover:text-primary-dark transition-colors"
              >
                {siteData.brand.email}
              </a>
            </div>
            
            <div>
              <h3 className="text-2xl font-display font-semibold text-foreground mb-4">
                Follow Us
              </h3>
              <div className="flex justify-center space-x-4">
                <a 
                  href={siteData.socials.instagram}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:text-primary-dark transition-colors"
                >
                  Instagram
                </a>
                <a 
                  href={siteData.socials.facebook}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:text-primary-dark transition-colors"
                >
                  Facebook
                </a>
                <a 
                  href={siteData.socials.tiktok}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-primary hover:text-primary-dark transition-colors"
                >
                  TikTok
                </a>
              </div>
            </div>
          </div>
        </div>
      </section>

      <Newsletter />
    </>
  )
}
