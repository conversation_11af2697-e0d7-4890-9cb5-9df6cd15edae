import { Metadata } from 'next'
import siteData from '@/content/site.json'

export const metadata: Metadata = {
  title: 'Co-working Space - Tea House Kigali',
  description: 'Productive co-working space with fast Wi-Fi, meeting rooms, and a peaceful atmosphere.',
}

export default function Coworking() {
  const cowork = siteData.cowork

  return (
    <>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-avocado/10 to-primary/10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl lg:text-6xl font-display font-bold text-foreground mb-6">
            Co-working Space
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            {cowork.blurb}
          </p>
          
          <div className="flex flex-wrap justify-center gap-4">
            <a 
              href="#features"
              className="bg-avocado hover:bg-avocado-dark text-white px-6 py-3 rounded-full font-medium transition-colors"
            >
              View Features
            </a>
            <a 
              href="#booking"
              className="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-full font-medium transition-colors"
            >
              Book Space
            </a>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-display font-bold text-foreground mb-4">
              Everything You Need to Work
            </h2>
            <p className="text-xl text-gray-600">
              A productive environment designed for focus and collaboration
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {cowork.features.map((feature, index) => (
              <div key={index} className="text-center p-6">
                <div className="w-16 h-16 bg-avocado/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-avocado-dark" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-2">{feature}</h3>
              </div>
            ))}
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            <div className="bg-gradient-to-br from-avocado/5 to-primary/5 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-display font-bold text-foreground mb-4">Day Pass</h3>
              <div className="text-4xl font-bold text-primary mb-2">
                {cowork.pricing.dayPass.price.toLocaleString()}
                <span className="text-lg text-gray-600 ml-2">RWF</span>
              </div>
              <p className="text-gray-600 mb-6">{cowork.pricing.dayPass.desc}</p>
              <ul className="text-left space-y-2 mb-8">
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-avocado-dark mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Open desk access
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-avocado-dark mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Free Wi-Fi & beverages
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-avocado-dark mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Printing access
                </li>
              </ul>
              <button className="w-full bg-avocado hover:bg-avocado-dark text-white py-3 rounded-full font-medium transition-colors">
                Book Day Pass
              </button>
            </div>

            <div className="bg-gradient-to-br from-primary/5 to-gold/5 rounded-2xl p-8 text-center">
              <h3 className="text-2xl font-display font-bold text-foreground mb-4">Meeting Room</h3>
              <div className="text-4xl font-bold text-primary mb-2">
                {cowork.pricing.meetingRoom.price.toLocaleString()}
                <span className="text-lg text-gray-600 ml-2">RWF</span>
              </div>
              <p className="text-gray-600 mb-6">{cowork.pricing.meetingRoom.desc}</p>
              <ul className="text-left space-y-2 mb-8">
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-primary mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Private room for up to 6 people
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-primary mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Whiteboard & presentation screen
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-primary mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  Complimentary refreshments
                </li>
              </ul>
              <button className="w-full bg-primary hover:bg-primary-dark text-white py-3 rounded-full font-medium transition-colors">
                Book Meeting Room
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Booking Section */}
      <section id="booking" className="py-16 bg-neutral-50">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-display font-bold text-foreground mb-4">
              Book Your Space
            </h2>
            <p className="text-xl text-gray-600">
              Reserve your co-working space or meeting room today
            </p>
          </div>

          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <form className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label htmlFor="space-type" className="block text-sm font-medium text-gray-700 mb-2">
                    Space Type *
                  </label>
                  <select
                    id="space-type"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  >
                    <option value="">Select space type</option>
                    <option value="day-pass">Day Pass - Open Desk</option>
                    <option value="meeting-room">Meeting Room</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-2">
                    Date *
                  </label>
                  <input
                    type="date"
                    id="date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="people" className="block text-sm font-medium text-gray-700 mb-2">
                    Number of People *
                  </label>
                  <input
                    type="number"
                    id="people"
                    min="1"
                    max="6"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="duration" className="block text-sm font-medium text-gray-700 mb-2">
                    Duration
                  </label>
                  <select
                    id="duration"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="full-day">Full Day</option>
                    <option value="half-day">Half Day (4 hours)</option>
                    <option value="hourly">Hourly</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="email"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div className="md:col-span-2">
                  <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-2">
                    Special Requirements
                  </label>
                  <textarea
                    id="notes"
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    placeholder="Any special equipment needs or other requirements..."
                  />
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  type="submit"
                  className="flex-1 bg-avocado hover:bg-avocado-dark text-white py-3 px-6 rounded-full font-medium transition-colors"
                >
                  Submit Booking Request
                </button>
                <a
                  href={`https://wa.me/${siteData.brand.whatsapp.replace(/[^0-9]/g, '')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 bg-gold hover:bg-gold-light text-white py-3 px-6 rounded-full font-medium text-center transition-colors"
                >
                  Book via WhatsApp
                </a>
              </div>

              <p className="text-sm text-gray-500 text-center">
                We will contact you within 24 hours to confirm your booking and arrange payment.
              </p>
            </form>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 bg-gradient-to-r from-avocado to-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-display font-bold mb-4">
            Questions About Our Co-working Space?
          </h2>
          <p className="text-xl opacity-90 mb-8">
            Get in touch with our team for more information or to schedule a tour
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href={`tel:${siteData.brand.phone}`}
              className="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              📞 Call Us
            </a>
            <a 
              href={`mailto:${siteData.brand.email}`}
              className="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              ✉️ Email Us
            </a>
          </div>
        </div>
      </section>
    </>
  )
}