import { Metadata } from 'next'
import MenuList from '@/components/MenuList'
import EventList from '@/components/EventList'
import FoodOrderForm from '@/components/FoodOrderForm'

export const metadata: Metadata = {
  title: 'Restaurant - Tea House Kigali',
  description: 'Discover our delicious menu featuring fresh, locally-sourced ingredients and signature tea blends.',
}

export default function Restaurant() {
  return (
    <>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 to-gold/10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl lg:text-6xl font-display font-bold text-foreground mb-6">
            Restaurant
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Experience fresh, locally-sourced cuisine with our signature tea blends. 
            From hearty breakfasts to elegant dinners, every meal is crafted with care.
          </p>
          
          {/* Quick Navigation */}
          <div className="flex flex-wrap justify-center gap-4">
            <a 
              href="#menu"
              className="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-full font-medium transition-colors"
            >
              View Menu
            </a>
            <a 
              href="#events"
              className="bg-gold hover:bg-gold-light text-white px-6 py-3 rounded-full font-medium transition-colors"
            >
              Special Events
            </a>
            <a 
              href="#order"
              className="bg-avocado hover:bg-avocado-dark text-white px-6 py-3 rounded-full font-medium transition-colors"
            >
              Order Food
            </a>
          </div>
        </div>
      </section>

      <MenuList />
      <EventList />
      <FoodOrderForm />
    </>
  )
}