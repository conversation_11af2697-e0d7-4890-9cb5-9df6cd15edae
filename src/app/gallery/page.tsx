'use client'

import { useState } from 'react'
import { Metadata } from 'next'
import Lightbox from '@/components/Lightbox'

const galleryImages = [
  // Restaurant & Food
  { src: '/images/gallery/restaurant-interior.jpg', alt: 'Restaurant interior', title: 'Restaurant Interior', category: 'Restaurant' },
  { src: '/images/gallery/breakfast-spread.jpg', alt: 'Breakfast spread', title: 'Fresh Breakfast', category: 'Food' },
  { src: '/images/gallery/avocado-toast.jpg', alt: 'Avocado toast', title: 'Signature Avocado Toast', category: 'Food' },
  { src: '/images/gallery/tea-service.jpg', alt: 'Tea service', title: 'Tea Tasting Service', category: 'Food' },
  { src: '/images/gallery/brunch-event.jpg', alt: 'Sunday brunch event', title: 'Sunday Brunch', category: 'Events' },
  
  // Co-working Space
  { src: '/images/gallery/cowork-space.jpg', alt: 'Co-working space', title: 'Co-working Area', category: 'Co-working' },
  { src: '/images/gallery/meeting-room.jpg', alt: 'Meeting room', title: 'Private Meeting Room', category: 'Co-working' },
  { src: '/images/gallery/work-desk.jpg', alt: 'Work desk setup', title: 'Productive Workspace', category: 'Co-working' },
  
  // Hotel & Spa
  { src: '/images/gallery/hotel-room.jpg', alt: 'Hotel room', title: 'Boutique Hotel Room', category: 'Hotel' },
  { src: '/images/gallery/massage-room.jpg', alt: 'Massage room', title: 'Relaxing Spa Room', category: 'Spa' },
  { src: '/images/gallery/hotel-bathroom.jpg', alt: 'Hotel bathroom', title: 'En-suite Bathroom', category: 'Hotel' },
  
  // Ambiance & Exterior
  { src: '/images/gallery/outdoor-seating.jpg', alt: 'Outdoor seating', title: 'Garden Seating Area', category: 'Ambiance' },
  { src: '/images/gallery/evening-lights.jpg', alt: 'Evening atmosphere', title: 'Evening Ambiance', category: 'Ambiance' },
  { src: '/images/gallery/entrance.jpg', alt: 'Tea House entrance', title: 'Welcome to Tea House', category: 'Exterior' },
  
  // Events
  { src: '/images/gallery/tea-tasting.jpg', alt: 'Tea tasting event', title: 'Tea Tasting Evening', category: 'Events' },
  { src: '/images/gallery/private-dining.jpg', alt: 'Private dining setup', title: 'Private Event Setup', category: 'Events' }
]

const categories = ['All', 'Restaurant', 'Food', 'Co-working', 'Hotel', 'Spa', 'Events', 'Ambiance', 'Exterior']

export default function Gallery() {
  const [selectedCategory, setSelectedCategory] = useState('All')
  const [lightboxIndex, setLightboxIndex] = useState(0)
  const [isLightboxOpen, setIsLightboxOpen] = useState(false)

  const filteredImages = selectedCategory === 'All' 
    ? galleryImages 
    : galleryImages.filter(image => image.category === selectedCategory)

  const openLightbox = (index: number) => {
    setLightboxIndex(index)
    setIsLightboxOpen(true)
  }

  const closeLightbox = () => {
    setIsLightboxOpen(false)
  }

  const nextImage = () => {
    setLightboxIndex((prev) => (prev + 1) % filteredImages.length)
  }

  const prevImage = () => {
    setLightboxIndex((prev) => (prev - 1 + filteredImages.length) % filteredImages.length)
  }

  return (
    <>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-primary/10 to-gold/10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl lg:text-6xl font-display font-bold text-foreground mb-6">
            Gallery
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore the beauty, comfort, and experiences that make Tea House Kigali special. 
            From delicious cuisine to relaxing spaces, discover what awaits you.
          </p>
        </div>
      </section>

      {/* Filter Categories */}
      <section className="py-8 bg-white sticky top-16 z-40 border-b border-neutral-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-6 py-2 rounded-full font-medium transition-colors ${
                  selectedCategory === category
                    ? 'bg-primary text-white'
                    : 'bg-neutral-100 text-gray-700 hover:bg-neutral-200'
                }`}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredImages.map((image, index) => (
              <div
                key={`${selectedCategory}-${index}`}
                className="group cursor-pointer"
                onClick={() => openLightbox(index)}
              >
                <div className="aspect-square bg-gradient-to-br from-avocado/20 to-gold/20 rounded-2xl overflow-hidden group-hover:scale-[1.02] transition-transform duration-300">
                  {/* Placeholder for actual image */}
                  <div className="w-full h-full flex items-center justify-center text-gray-500 relative">
                    <div className="text-center">
                      <svg className="w-16 h-16 mx-auto mb-2 text-primary" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                      </svg>
                      <p className="text-sm font-medium">{image.title}</p>
                      <p className="text-xs text-gray-400">{image.category}</p>
                    </div>
                    
                    {/* Hover Overlay */}
                    <div className="absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors duration-300 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <svg className="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM10 7v3m0 0v3m0-3h3m-3 0H7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {filteredImages.length === 0 && (
            <div className="text-center py-16">
              <p className="text-xl text-gray-500">No images found in this category.</p>
            </div>
          )}
        </div>
      </section>

      {/* Gallery Stats */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-primary mb-2">{galleryImages.length}+</div>
              <p className="text-gray-600">Beautiful Moments Captured</p>
            </div>
            <div>
              <div className="text-4xl font-bold text-gold mb-2">{categories.length - 1}</div>
              <p className="text-gray-600">Different Experiences</p>
            </div>
            <div>
              <div className="text-4xl font-bold text-avocado-dark mb-2">3</div>
              <p className="text-gray-600">Unique Services</p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-avocado to-primary text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-display font-bold mb-4">
            Ready to Experience Tea House Kigali?
          </h2>
          <p className="text-xl opacity-90 mb-8">
            Come create your own memories in our beautiful spaces
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="/restaurant#events"
              className="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              Book a Table
            </a>
            <a 
              href="/hotel#rooms"
              className="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              Book a Room
            </a>
            <a 
              href="/contact"
              className="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              Get in Touch
            </a>
          </div>
        </div>
      </section>

      {/* Lightbox */}
      <Lightbox
        images={filteredImages}
        currentIndex={lightboxIndex}
        isOpen={isLightboxOpen}
        onClose={closeLightbox}
        onNext={nextImage}
        onPrevious={prevImage}
      />
    </>
  )
}