@import "tailwindcss";

:root {
  /* Enhanced Color Palette */
  --background: #ffffff;
  --foreground: #1a1a1a;

  /* Primary Colors - <PERSON> */
  --primary: #2d5016;
  --primary-light: #4a7c2a;
  --primary-dark: #1e3a0f;

  /* Secondary Colors - Warm Sage */
  --secondary: #7a8471;
  --secondary-light: #9ba08f;
  --secondary-dark: #5c6354;

  /* Accent Colors - Sophisticated Gold */
  --accent: #c9a96e;
  --accent-light: #e4c896;
  --accent-dark: #a08751;

  /* Semantic Colors */
  --success: #22c55e;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Neutral Palette */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Legacy color mappings for backward compatibility */
  --avocado: var(--secondary);
  --avocado-dark: var(--secondary-dark);
  --gold: var(--accent);
  --gold-light: var(--accent-light);
}

@theme inline {
  /* Color System */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-light: var(--primary-light);
  --color-primary-dark: var(--primary-dark);
  --color-secondary: var(--secondary);
  --color-secondary-light: var(--secondary-light);
  --color-secondary-dark: var(--secondary-dark);
  --color-accent: var(--accent);
  --color-accent-light: var(--accent-light);
  --color-accent-dark: var(--accent-dark);

  /* Semantic Colors */
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);
  --color-info: var(--info);

  /* Neutral Colors */
  --color-neutral-50: var(--neutral-50);
  --color-neutral-100: var(--neutral-100);
  --color-neutral-200: var(--neutral-200);
  --color-neutral-300: var(--neutral-300);
  --color-neutral-400: var(--neutral-400);
  --color-neutral-500: var(--neutral-500);
  --color-neutral-600: var(--neutral-600);
  --color-neutral-700: var(--neutral-700);
  --color-neutral-800: var(--neutral-800);
  --color-neutral-900: var(--neutral-900);

  /* Legacy color mappings */
  --color-avocado: var(--avocado);
  --color-avocado-dark: var(--avocado-dark);
  --color-gold: var(--gold);
  --color-gold-light: var(--gold-light);

  /* Typography System */
  --font-sans: var(--font-inter);
  --font-display: var(--font-poppins);

  /* Font Sizes */
  --text-xs: 0.75rem;     /* 12px */
  --text-sm: 0.875rem;    /* 14px */
  --text-base: 1rem;      /* 16px */
  --text-lg: 1.125rem;    /* 18px */
  --text-xl: 1.25rem;     /* 20px */
  --text-2xl: 1.5rem;     /* 24px */
  --text-3xl: 1.875rem;   /* 30px */
  --text-4xl: 2.25rem;    /* 36px */
  --text-5xl: 3rem;       /* 48px */
  --text-6xl: 3.75rem;    /* 60px */

  /* Line Heights */
  --leading-tight: 1.25;
  --leading-snug: 1.375;
  --leading-normal: 1.5;
  --leading-relaxed: 1.625;
  --leading-loose: 2;

  /* Spacing Scale */
  --spacing-xs: 0.5rem;   /* 8px */
  --spacing-sm: 0.75rem;  /* 12px */
  --spacing-md: 1rem;     /* 16px */
  --spacing-lg: 1.5rem;   /* 24px */
  --spacing-xl: 2rem;     /* 32px */
  --spacing-2xl: 3rem;    /* 48px */
  --spacing-3xl: 4rem;    /* 64px */
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0f0f0f;
    --foreground: #f5f5f5;
    --neutral-50: #262626;
    --neutral-100: #404040;
    --neutral-200: #525252;
    --neutral-900: #f5f5f5;
  }
}

/* Base Typography and Layout */
body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, -apple-system, sans-serif;
  font-size: var(--text-base);
  line-height: var(--leading-normal);
  font-weight: 400;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Enhanced Typography Classes */
.text-display {
  font-family: var(--font-display), var(--font-sans), system-ui, sans-serif;
  font-weight: 600;
  line-height: var(--leading-tight);
  letter-spacing: -0.025em;
}

.text-heading {
  font-family: var(--font-display), var(--font-sans), system-ui, sans-serif;
  font-weight: 500;
  line-height: var(--leading-snug);
  letter-spacing: -0.015em;
}

.text-body {
  font-family: var(--font-sans), system-ui, sans-serif;
  line-height: var(--leading-relaxed);
}

.text-caption {
  font-family: var(--font-sans), system-ui, sans-serif;
  font-size: var(--text-sm);
  line-height: var(--leading-normal);
  color: var(--neutral-600);
}

/* Focus and Interaction States */
.focus-ring {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

/* Smooth Transitions */
.transition-smooth {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Button Styles */
.btn-primary {
  background: var(--primary);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  font-size: var(--text-base);
  line-height: 1;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-primary:hover {
  background: var(--primary-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(45, 80, 22, 0.3);
}

.btn-secondary {
  background: var(--accent);
  color: white;
  border: none;
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  font-size: var(--text-base);
  line-height: 1;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-secondary:hover {
  background: var(--accent-dark);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(201, 169, 110, 0.3);
}
