import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Terms of Service - Tea House Kigali',
  description: 'Terms of Service for Tea House Kigali.',
}

export default function Terms() {
  return (
    <div className="min-h-screen py-16">
      <div className="max-w-4xl mx-auto px-4">
        <h1 className="text-4xl font-display font-bold mb-8">Terms of Service</h1>
        <div className="prose prose-lg max-w-none">
          <p className="text-lg text-gray-600 mb-6">Last updated: {new Date().toLocaleDateString()}</p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Acceptance of Terms</h2>
          <p>By accessing and using Tea House Kigali services, you accept and agree to be bound by the terms and provision of this agreement.</p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Reservations</h2>
          <p>All reservations are subject to availability. We reserve the right to cancel or modify reservations as necessary.</p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Cancellation Policy</h2>
          <p>Cancellations must be made at least 24 hours in advance for restaurant reservations and 48 hours for hotel bookings.</p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Liability</h2>
          <p>Tea House Kigali shall not be liable for any indirect, incidental, special, consequential, or punitive damages.</p>
          
          <h2 className="text-2xl font-semibold mt-8 mb-4">Contact</h2>
          <p>For questions about these Terms of Service, please contact <NAME_EMAIL>.</p>
        </div>
      </div>
    </div>
  )
}