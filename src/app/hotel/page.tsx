import { Metadata } from 'next'
import siteData from '@/content/site.json'

export const metadata: Metadata = {
  title: 'Boutique Hotel - Tea House Kigali',
  description: 'Comfortable boutique hotel rooms with breakfast included, plus relaxing massage services.',
}

export default function Hotel() {
  const hotel = siteData.hotel

  return (
    <>
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-gold/10 to-primary/10 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl lg:text-6xl font-display font-bold text-foreground mb-6">
            Boutique Hotel
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
            Experience comfort and hospitality at Tea House Kigali. Our boutique hotel 
            offers cozy rooms with breakfast included and rejuvenating spa services.
          </p>
          
          <div className="flex flex-wrap justify-center gap-4">
            <a 
              href="#rooms"
              className="bg-primary hover:bg-primary-dark text-white px-6 py-3 rounded-full font-medium transition-colors"
            >
              View Rooms
            </a>
            <a 
              href="#massage"
              className="bg-gold hover:bg-gold-light text-white px-6 py-3 rounded-full font-medium transition-colors"
            >
              Spa Services
            </a>
          </div>
        </div>
      </section>

      {/* Rooms Section */}
      <section id="rooms" className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-display font-bold text-foreground mb-4">
              {hotel.rooms.title}
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              {hotel.rooms.description}
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <div>
              <div className="aspect-video bg-gradient-to-br from-primary/20 to-gold/20 rounded-2xl flex items-center justify-center">
                <div className="text-center text-gray-500">
                  <svg className="w-20 h-20 mx-auto mb-4 text-primary" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                  </svg>
                  <p className="text-sm">Hotel Room Image</p>
                </div>
              </div>
            </div>
            
            <div>
              <h3 className="text-3xl font-display font-bold text-foreground mb-6">
                Comfortable Accommodations
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                {hotel.rooms.features.map((feature, index) => (
                  <div key={index} className="flex items-center">
                    <svg className="w-5 h-5 text-primary mr-3" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
              <button className="bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-full font-medium transition-colors">
                Book Room
              </button>
            </div>
          </div>

          {/* Room Booking Form */}
          <div className="bg-neutral-50 rounded-2xl p-8">
            <h3 className="text-2xl font-display font-bold text-center text-foreground mb-8">
              Book Your Stay
            </h3>
            <form className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
                <div>
                  <label htmlFor="checkin" className="block text-sm font-medium text-gray-700 mb-2">
                    Check-in *
                  </label>
                  <input
                    type="date"
                    id="checkin"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="checkout" className="block text-sm font-medium text-gray-700 mb-2">
                    Check-out *
                  </label>
                  <input
                    type="date"
                    id="checkout"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="guests" className="block text-sm font-medium text-gray-700 mb-2">
                    Adults *
                  </label>
                  <select
                    id="guests"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  >
                    <option value="1">1 Adult</option>
                    <option value="2">2 Adults</option>
                    <option value="3">3 Adults</option>
                    <option value="4">4 Adults</option>
                  </select>
                </div>
                
                <div>
                  <label htmlFor="rooms" className="block text-sm font-medium text-gray-700 mb-2">
                    Rooms *
                  </label>
                  <select
                    id="rooms"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  >
                    <option value="1">1 Room</option>
                    <option value="2">2 Rooms</option>
                    <option value="3">3 Rooms</option>
                  </select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="guest-name" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="guest-name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="guest-phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="guest-phone"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="guest-email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email *
                  </label>
                  <input
                    type="email"
                    id="guest-email"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="children" className="block text-sm font-medium text-gray-700 mb-2">
                    Children (under 12)
                  </label>
                  <select
                    id="children"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  >
                    <option value="0">No children</option>
                    <option value="1">1 Child</option>
                    <option value="2">2 Children</option>
                    <option value="3">3 Children</option>
                  </select>
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="special-requests" className="block text-sm font-medium text-gray-700 mb-2">
                  Special Requests
                </label>
                <textarea
                  id="special-requests"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                  placeholder="Any special requests, dietary requirements, or other notes..."
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  type="submit"
                  className="flex-1 bg-primary hover:bg-primary-dark text-white py-3 px-6 rounded-full font-medium transition-colors"
                >
                  Request Booking
                </button>
                <a
                  href={`https://wa.me/${siteData.brand.whatsapp.replace(/[^0-9]/g, '')}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex-1 bg-gold hover:bg-gold-light text-white py-3 px-6 rounded-full font-medium text-center transition-colors"
                >
                  Book via WhatsApp
                </a>
              </div>

              <p className="text-sm text-gray-500 text-center mt-4">
                * We will contact you within 24 hours to confirm your booking and arrange payment.
              </p>
            </form>
          </div>
        </div>
      </section>

      {/* Massage Services Section */}
      <section id="massage" className="py-16 bg-neutral-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-display font-bold text-foreground mb-4">
              {hotel.massage.title}
            </h2>
            <p className="text-xl text-gray-600">
              Relax and rejuvenate with our professional massage services
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            {hotel.massage.services.map((service, index) => (
              <div key={index} className="bg-white rounded-2xl p-8 shadow-sm hover:shadow-lg transition-shadow">
                <div className="flex justify-between items-start mb-4">
                  <h3 className="text-2xl font-display font-bold text-foreground">{service.name}</h3>
                  <div className="text-right">
                    <div className="text-2xl font-bold text-primary">{service.price.toLocaleString()}</div>
                    <div className="text-sm text-gray-600">RWF</div>
                  </div>
                </div>
                <div className="flex items-center text-gray-600 mb-6">
                  <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span>{service.duration}</span>
                </div>
                <button className="w-full bg-gold hover:bg-gold-light text-white py-3 rounded-full font-medium transition-colors">
                  Book {service.name}
                </button>
              </div>
            ))}
          </div>

          {/* Massage Booking Form */}
          <div className="bg-white rounded-2xl p-8 shadow-sm">
            <h3 className="text-2xl font-display font-bold text-center text-foreground mb-8">
              Book a Massage
            </h3>
            <form className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label htmlFor="massage-service" className="block text-sm font-medium text-gray-700 mb-2">
                    Service *
                  </label>
                  <select
                    id="massage-service"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent"
                    required
                  >
                    <option value="">Select a service</option>
                    {hotel.massage.services.map((service, index) => (
                      <option key={index} value={service.name}>
                        {service.name} - {service.duration} ({service.price.toLocaleString()} RWF)
                      </option>
                    ))}
                  </select>
                </div>
                
                <div>
                  <label htmlFor="massage-date" className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Date *
                  </label>
                  <input
                    type="date"
                    id="massage-date"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="massage-time" className="block text-sm font-medium text-gray-700 mb-2">
                    Preferred Time *
                  </label>
                  <select
                    id="massage-time"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent"
                    required
                  >
                    <option value="">Select time</option>
                    <option value="09:00">9:00 AM</option>
                    <option value="11:00">11:00 AM</option>
                    <option value="14:00">2:00 PM</option>
                    <option value="16:00">4:00 PM</option>
                    <option value="18:00">6:00 PM</option>
                  </select>
                </div>

                <div>
                  <label htmlFor="massage-name" className="block text-sm font-medium text-gray-700 mb-2">
                    Full Name *
                  </label>
                  <input
                    type="text"
                    id="massage-name"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="massage-phone" className="block text-sm font-medium text-gray-700 mb-2">
                    Phone Number *
                  </label>
                  <input
                    type="tel"
                    id="massage-phone"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="massage-email" className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    id="massage-email"
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent"
                  />
                </div>
              </div>

              <div className="mb-6">
                <label htmlFor="massage-notes" className="block text-sm font-medium text-gray-700 mb-2">
                  Special Notes
                </label>
                <textarea
                  id="massage-notes"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gold focus:border-transparent"
                  placeholder="Any health conditions, preferences, or special requests..."
                />
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <button
                  type="submit"
                  className="flex-1 bg-gold hover:bg-gold-light text-white py-3 px-6 rounded-full font-medium transition-colors"
                >
                  Book Massage
                </button>
                <a
                  href={`tel:${siteData.brand.phone}`}
                  className="flex-1 bg-primary hover:bg-primary-dark text-white py-3 px-6 rounded-full font-medium text-center transition-colors"
                >
                  Call to Book
                </a>
              </div>

              <p className="text-sm text-gray-500 text-center mt-4">
                * We will contact you to confirm availability and arrange your appointment.
              </p>
            </form>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-16 bg-gradient-to-r from-primary to-gold text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-display font-bold mb-4">
            Ready for a Relaxing Stay?
          </h2>
          <p className="text-xl opacity-90 mb-8">
            Experience comfort, hospitality, and rejuvenation at Tea House Kigali
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a 
              href="#rooms"
              className="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              Book a Room
            </a>
            <a 
              href="#massage"
              className="bg-white/20 hover:bg-white/30 text-white px-8 py-3 rounded-full font-medium transition-colors"
            >
              Book a Massage
            </a>
          </div>
        </div>
      </section>
    </>
  )
}